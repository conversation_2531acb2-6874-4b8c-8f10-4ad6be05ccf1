/* CSS Variables for better maintainability */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #38a169;
    --error-color: #e53e3e;
    --warning-color: #f56565;
    --bg-light: #f8fafc;
    --bg-lighter: #f1f5f9;
    --border-color: #e2e8f0;
    --border-dashed: #cbd5e0;
    --text-primary: #2d3748;
    --text-secondary: #4a5568;
    --shadow-light: 0 5px 15px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 10px 20px rgba(102, 126, 234, 0.3);
    --shadow-heavy: 0 20px 40px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    --border-radius: 10px;
    --border-radius-large: 15px;
    --border-radius-xl: 20px;
}

/* Base styles */
* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON>e UI', <PERSON><PERSON>, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-xl);
    padding: 30px;
    box-shadow: var(--shadow-heavy);
}

/* Typography */
h1 {
    text-align: center;
    color: var(--text-secondary);
    margin-bottom: 30px;
    font-size: clamp(2rem, 4vw, 2.5rem);
    font-weight: 700;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

h2 {
    color: var(--text-secondary);
    margin-bottom: 20px;
    font-size: 1.5rem;
}

h3 {
    color: var(--text-secondary);
    margin-bottom: 10px;
    font-size: 1.2em;
}

/* Layout components */
.input-section {
    margin-bottom: 30px;
    padding: 25px;
    background: var(--bg-light);
    border-radius: var(--border-radius-large);
    border: 2px dashed var(--border-dashed);
    transition: var(--transition);
}

.input-section:hover {
    border-color: var(--primary-color);
    background: var(--bg-lighter);
}

.results-section {
    margin-top: 30px;
    padding: 25px;
    background: linear-gradient(135deg, var(--bg-light) 0%, var(--border-color) 100%);
    border-radius: var(--border-radius-large);
    border-left: 5px solid var(--primary-color);
}

.hidden {
    display: none !important;
}

/* Form elements */
textarea {
    width: 100%;
    height: 150px;
    padding: 15px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: 'Courier New', monospace;
    font-size: 14px;
    resize: vertical;
    transition: border-color 0.3s ease;
    background: white;
}

textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Button styles */
.button-group {
    display: flex;
    gap: 15px;
    margin-top: 20px;
    flex-wrap: wrap;
}

button {
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    font-size: 16px;
    position: relative;
    overflow: hidden;
    color: white;
}

button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

button:hover::before {
    left: 100%;
}

button:hover {
    transform: translateY(-2px);
}

.solve-btn {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    flex: 1;
    min-width: 200px;
}

.solve-btn:hover {
    box-shadow: var(--shadow-medium);
}

.clear-btn {
    background: linear-gradient(45deg, var(--warning-color), var(--error-color));
}

.clear-btn:hover {
    box-shadow: 0 10px 20px rgba(245, 101, 101, 0.3);
}

.load-example-btn {
    background: linear-gradient(45deg, #48bb78, var(--success-color));
}

.load-example-btn:hover {
    box-shadow: 0 10px 20px rgba(72, 187, 120, 0.3);
}

/* Results display */
.svg-preview {
    margin: 20px 0;
    padding: 20px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    text-align: center;
    overflow-x: auto;
}

.result-text {
    font-size: clamp(1.2rem, 3vw, 1.5rem);
    font-weight: bold;
    color: var(--text-primary);
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    padding: 15px;
    text-align: center;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    margin: 15px 0;
    word-break: break-all;
}

/* Grid layouts */
.analysis-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.analysis-card {
    background: white;
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: transform 0.3s ease;
}

.analysis-card:hover {
    transform: translateY(-5px);
}

.character-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.character-box {
    padding: 8px 12px;
    background: var(--primary-color);
    color: white;
    border-radius: 8px;
    font-weight: bold;
    font-size: 18px;
    min-width: 40px;
    text-align: center;
    transition: var(--transition);
}

.character-box:hover {
    background: var(--secondary-color);
    transform: scale(1.05);
}

/* Loading and status indicators */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error {
    color: var(--error-color);
    background: #fed7d7;
    padding: 15px;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--error-color);
    margin: 10px 0;
}

.success {
    color: var(--success-color);
    background: #c6f6d5;
    padding: 15px;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--success-color);
    margin: 10px 0;
}

/* Responsive design */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    .container {
        padding: 20px;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    button {
        width: 100%;
        min-width: auto;
    }
    
    .analysis-grid {
        grid-template-columns: 1fr;
    }
    
    textarea {
        height: 120px;
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .input-section,
    .results-section {
        padding: 15px;
    }
    
    .character-box {
        font-size: 16px;
        padding: 6px 10px;
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus styles for better accessibility */
button:focus,
textarea:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Print styles */
@media print {
    body {
        background: white;
        color: black;
    }
    
    .container {
        box-shadow: none;
        background: white;
    }
    
    button {
        display: none;
    }
}
